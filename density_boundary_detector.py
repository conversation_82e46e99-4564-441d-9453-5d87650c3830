"""
概率密度边界检测模块
用于船舶领域拟合中的扇区划分和边界检测

功能：
1. 扇区划分：将相对位置数据按角度划分为扇区
2. 概率密度计算：计算每个扇区的点密度分布
3. 边界检测：基于密度突变检测边界点
4. 可视化支持：提供密度分布和边界点的可视化

作者：船舶领域拟合系统
日期：2025-08-01
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.ndimage import gaussian_filter1d
from scipy import stats
import math
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from tqdm import tqdm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


@dataclass
class SectorData:
    """扇区数据结构"""
    sector_id: int
    angle_range: Tuple[float, float]  # (start_angle, end_angle)
    points: List[Dict[str, float]]  # 扇区内的点
    distances: List[float]  # 距离列表
    density: float  # 密度值
    boundary_distance: Optional[float] = None  # 边界距离


@dataclass
class DensityProfile:
    """密度分布轮廓"""
    distances: np.ndarray
    density: np.ndarray
    raw_hist: np.ndarray
    bin_edges: np.ndarray
    smoothed_density: np.ndarray


class DensityBoundaryDetector:
    """概率密度边界检测器"""
    
    def __init__(self, sector_angle: float = 5.0, debug: bool = False):
        """
        初始化边界检测器
        
        Args:
            sector_angle: 扇区角度（度），默认5度
            debug: 是否开启调试模式
        """
        self.sector_angle = sector_angle
        self.debug = debug
        self.num_sectors = int(360 / sector_angle)
        
        # 存储数据
        self.sectors: Dict[int, SectorData] = {}
        self.boundary_points: List[Tuple[float, float]] = []
        self.density_profiles: Dict[int, DensityProfile] = {}
        
        print(f"🎯 密度边界检测器初始化完成")
        print(f"   扇区角度: {sector_angle}°")
        print(f"   扇区数量: {self.num_sectors}")
    
    def divide_into_sectors(self, x_coords: np.ndarray, y_coords: np.ndarray) -> Dict[int, SectorData]:
        """
        将相对位置数据划分为扇区
        
        Args:
            x_coords: X坐标数组
            y_coords: Y坐标数组
            
        Returns:
            扇区数据字典
        """
        print(f"\n=== 扇区划分 ===")
        print(f"输入数据点: {len(x_coords)} 个")
        
        # 初始化扇区
        for sector_id in range(self.num_sectors):
            start_angle = sector_id * self.sector_angle
            end_angle = (sector_id + 1) * self.sector_angle
            self.sectors[sector_id] = SectorData(
                sector_id=sector_id,
                angle_range=(start_angle, end_angle),
                points=[],
                distances=[],
                density=0.0
            )
        
        # 计算每个点的角度和距离
        angles = np.degrees(np.arctan2(y_coords, x_coords))
        angles = (angles + 360) % 360  # 转换为0-360度
        distances = np.sqrt(x_coords**2 + y_coords**2)
        
        # 分配点到扇区
        for i, (angle, distance) in enumerate(zip(angles, distances)):
            sector_id = int(angle // self.sector_angle) % self.num_sectors
            
            point_data = {
                'x': x_coords[i],
                'y': y_coords[i],
                'angle': angle,
                'distance': distance,
                'index': i
            }
            
            self.sectors[sector_id].points.append(point_data)
            self.sectors[sector_id].distances.append(distance)
        
        # 计算每个扇区的基础密度
        total_points = len(x_coords)
        for sector_id, sector in self.sectors.items():
            sector.density = len(sector.points) / total_points if total_points > 0 else 0.0
        
        # 统计信息
        non_empty_sectors = sum(1 for s in self.sectors.values() if len(s.points) > 0)
        print(f"✅ 扇区划分完成:")
        print(f"   非空扇区: {non_empty_sectors}/{self.num_sectors}")
        print(f"   平均每扇区: {total_points/self.num_sectors:.1f} 个点")
        
        return self.sectors
    
    def calculate_sector_density_profiles(self) -> Dict[int, DensityProfile]:
        """计算每个扇区的密度分布轮廓"""
        print(f"\n=== 计算扇区密度分布 ===")
        
        for sector_id, sector in tqdm(self.sectors.items(), desc="计算密度分布"):
            if len(sector.distances) < 5:  # 点太少跳过
                continue
                
            try:
                # 计算密度分布
                profile = self._calculate_density_profile(sector.distances)
                if profile is not None:
                    self.density_profiles[sector_id] = profile
                    
            except Exception as e:
                if self.debug:
                    print(f"扇区 {sector_id} 密度计算失败: {e}")
                continue
        
        print(f"✅ 密度分布计算完成: {len(self.density_profiles)} 个扇区")
        return self.density_profiles
    
    def _calculate_density_profile(self, distances: List[float]) -> Optional[DensityProfile]:
        """计算单个扇区的密度分布轮廓"""
        try:
            distances = np.array(distances)
            if len(distances) < 5:
                return None
            
            # 使用自适应分箱
            num_bins = min(max(int(len(distances) / 10), 10), 50)
            
            # 计算直方图
            hist, bin_edges = np.histogram(distances, bins=num_bins, density=True)
            bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
            
            # 高斯平滑
            sigma = max(1.0, len(distances) / 100)  # 自适应平滑参数
            smoothed_density = gaussian_filter1d(hist, sigma=sigma)
            
            return DensityProfile(
                distances=bin_centers,
                density=hist,
                raw_hist=hist,
                bin_edges=bin_edges,
                smoothed_density=smoothed_density
            )
            
        except Exception as e:
            if self.debug:
                print(f"密度分布计算失败: {e}")
            return None
    
    def detect_boundary_points(self, method: str = 'gradient') -> List[Tuple[float, float]]:
        """
        检测边界点
        
        Args:
            method: 检测方法 ('gradient', 'percentile', 'mutation')
            
        Returns:
            边界点坐标列表
        """
        print(f"\n=== 边界点检测 (方法: {method}) ===")
        
        self.boundary_points = []
        
        if method == 'gradient':
            self._detect_boundary_by_gradient()
        elif method == 'percentile':
            self._detect_boundary_by_percentile()
        elif method == 'mutation':
            self._detect_boundary_by_mutation()
        else:
            raise ValueError(f"未知的检测方法: {method}")
        
        print(f"✅ 边界点检测完成: {len(self.boundary_points)} 个边界点")
        return self.boundary_points
    
    def _detect_boundary_by_gradient(self):
        """基于梯度变化检测边界点"""
        for sector_id, sector in self.sectors.items():
            if sector_id not in self.density_profiles or len(sector.points) < 10:
                continue
                
            try:
                profile = self.density_profiles[sector_id]
                
                # 计算密度梯度
                gradient = np.gradient(profile.smoothed_density)
                
                # 寻找梯度变化最大的点
                gradient_change = np.abs(np.gradient(gradient))
                
                if len(gradient_change) > 0:
                    # 找到梯度变化最大的点
                    max_change_idx = np.argmax(gradient_change)
                    boundary_distance = profile.distances[max_change_idx]
                    
                    # 验证边界距离的合理性
                    if 50 <= boundary_distance <= 2000:
                        # 找到该距离附近的实际点
                        boundary_point = self._find_boundary_point_in_sector(sector, boundary_distance)
                        if boundary_point is not None:
                            self.boundary_points.append(boundary_point)
                            sector.boundary_distance = boundary_distance
                
            except Exception as e:
                if self.debug:
                    print(f"扇区 {sector_id} 梯度检测失败: {e}")
                continue
    
    def _detect_boundary_by_percentile(self, percentile: float = 85.0):
        """基于百分位数检测边界点"""
        for sector_id, sector in self.sectors.items():
            if len(sector.distances) < 10:
                continue
                
            try:
                # 使用百分位数作为边界
                boundary_distance = np.percentile(sector.distances, percentile)
                
                if 50 <= boundary_distance <= 2000:
                    boundary_point = self._find_boundary_point_in_sector(sector, boundary_distance)
                    if boundary_point is not None:
                        self.boundary_points.append(boundary_point)
                        sector.boundary_distance = boundary_distance
                        
            except Exception as e:
                if self.debug:
                    print(f"扇区 {sector_id} 百分位数检测失败: {e}")
                continue
    
    def _detect_boundary_by_mutation(self):
        """基于密度突变检测边界点"""
        for sector_id, sector in self.sectors.items():
            if sector_id not in self.density_profiles or len(sector.points) < 10:
                continue
                
            try:
                profile = self.density_profiles[sector_id]
                
                # 寻找密度突然下降的点
                density_diff = np.diff(profile.smoothed_density)
                
                # 找到最大的负梯度（密度下降最快的点）
                negative_gradients = density_diff[density_diff < 0]
                if len(negative_gradients) > 0:
                    min_gradient_idx = np.argmin(density_diff)
                    boundary_distance = profile.distances[min_gradient_idx]
                    
                    if 50 <= boundary_distance <= 2000:
                        boundary_point = self._find_boundary_point_in_sector(sector, boundary_distance)
                        if boundary_point is not None:
                            self.boundary_points.append(boundary_point)
                            sector.boundary_distance = boundary_distance
                
            except Exception as e:
                if self.debug:
                    print(f"扇区 {sector_id} 突变检测失败: {e}")
                continue
    
    def _find_boundary_point_in_sector(self, sector: SectorData, target_distance: float) -> Optional[Tuple[float, float]]:
        """在扇区中找到最接近目标距离的点"""
        if not sector.points:
            return None
            
        # 找到距离目标距离最近的点
        min_diff = float('inf')
        best_point = None
        
        for point in sector.points:
            diff = abs(point['distance'] - target_distance)
            if diff < min_diff:
                min_diff = diff
                best_point = (point['x'], point['y'])
        
        return best_point
    
    def get_boundary_statistics(self) -> Dict[str, Any]:
        """获取边界检测统计信息"""
        if not self.boundary_points:
            return {}
        
        # 计算边界点的距离统计
        boundary_distances = [math.sqrt(x**2 + y**2) for x, y in self.boundary_points]
        
        stats_dict = {
            'boundary_point_count': len(self.boundary_points),
            'mean_boundary_distance': np.mean(boundary_distances),
            'std_boundary_distance': np.std(boundary_distances),
            'min_boundary_distance': np.min(boundary_distances),
            'max_boundary_distance': np.max(boundary_distances),
            'sectors_with_boundary': sum(1 for s in self.sectors.values() if s.boundary_distance is not None),
            'total_sectors': len(self.sectors)
        }
        
        return stats_dict
    
    def save_results(self, output_path: str):
        """保存检测结果"""
        import pickle
        
        results = {
            'sectors': self.sectors,
            'boundary_points': self.boundary_points,
            'density_profiles': self.density_profiles,
            'statistics': self.get_boundary_statistics(),
            'parameters': {
                'sector_angle': self.sector_angle,
                'num_sectors': self.num_sectors
            }
        }
        
        with open(output_path, 'wb') as f:
            pickle.dump(results, f)
        
        print(f"✅ 结果已保存到: {output_path}")


def main():
    """测试主函数"""
    # 生成测试数据
    np.random.seed(42)
    n_points = 1000
    
    # 生成椭圆分布的测试数据
    angles = np.random.uniform(0, 2*np.pi, n_points)
    radii = np.random.gamma(2, 100, n_points)  # 伽马分布模拟真实数据
    
    x_coords = radii * np.cos(angles) + np.random.normal(0, 20, n_points)
    y_coords = radii * np.sin(angles) + np.random.normal(0, 20, n_points)
    
    print(f"🧪 测试数据生成完成: {n_points} 个点")
    
    # 创建检测器
    detector = DensityBoundaryDetector(sector_angle=5.0, debug=True)
    
    # 执行检测流程
    sectors = detector.divide_into_sectors(x_coords, y_coords)
    profiles = detector.calculate_sector_density_profiles()
    boundary_points = detector.detect_boundary_points(method='gradient')
    
    # 打印统计信息
    stats = detector.get_boundary_statistics()
    print(f"\n📊 边界检测统计:")
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # 保存结果
    detector.save_results('test_boundary_detection_results.pkl')


if __name__ == '__main__':
    main()
