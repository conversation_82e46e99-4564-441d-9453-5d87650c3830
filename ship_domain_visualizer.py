"""
船舶领域可视化模块
用于船舶领域拟合结果的可视化展示

功能：
1. 数据点散布图：显示相对位置数据点分布
2. 扇区可视化：显示扇区划分和密度分布
3. 椭圆边界绘制：显示拟合的椭圆边界
4. 密度分布图：显示概率密度分布
5. 拟合质量评估图：显示拟合质量指标
6. 对比分析图：对比不同方法的拟合结果

独立运行：python ship_domain_visualizer.py

作者：船舶领域拟合系统
日期：2025-08-01
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import LinearSegmentedColormap
from typing import List, Dict, Tuple, Optional, Any
import math
from pathlib import Path

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


class ShipDomainVisualizer:
    """船舶领域可视化器"""
    
    def __init__(self, figsize: Tuple[int, int] = (12, 8), dpi: int = 100):
        """
        初始化可视化器
        
        Args:
            figsize: 图形尺寸
            dpi: 图形分辨率
        """
        self.figsize = figsize
        self.dpi = dpi
        
        # 颜色配置
        self.colors = {
            'data_points': '#1f77b4',
            'boundary_points': '#ff7f0e',
            'ellipse': '#d62728',
            'sectors': '#2ca02c',
            'density': '#9467bd'
        }
        
        print(f"🎨 船舶领域可视化器初始化完成")
    
    def plot_data_distribution(self, x_coords: np.ndarray, y_coords: np.ndarray, 
                             title: str = "船舶相对位置分布", 
                             save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制数据点分布图
        
        Args:
            x_coords: X坐标数组
            y_coords: Y坐标数组
            title: 图标题
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig, ax = plt.subplots(figsize=self.figsize, dpi=self.dpi)
        
        # 绘制散点图
        scatter = ax.scatter(x_coords, y_coords, 
                           c=self.colors['data_points'], 
                           alpha=0.6, s=20, 
                           label=f'数据点 ({len(x_coords)}个)')
        
        # 设置坐标轴
        ax.set_xlabel('横向距离 (m)', fontsize=12)
        ax.set_ylabel('纵向距离 (m)', fontsize=12)
        ax.set_title(title, fontsize=14, fontweight='bold')
        
        # 添加原点标记
        ax.scatter(0, 0, c='red', s=100, marker='x', linewidth=3, label='本船位置')
        
        # 设置等比例和网格
        ax.set_aspect('equal', adjustable='box')
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # 添加统计信息
        stats_text = f'点数: {len(x_coords)}\n'
        stats_text += f'X范围: [{np.min(x_coords):.0f}, {np.max(x_coords):.0f}]m\n'
        stats_text += f'Y范围: [{np.min(y_coords):.0f}, {np.max(y_coords):.0f}]m'
        
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            print(f"✅ 数据分布图已保存: {save_path}")
        
        return fig
    
    def plot_sector_distribution(self, sectors_data: Dict[int, Dict],
                               title: str = "扇区分析",
                               save_path: Optional[str] = None) -> plt.Figure:
        """绘制扇区分析图"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12), dpi=self.dpi)

        # 提取扇区数据
        sector_ids = list(sectors_data.keys())
        sector_counts = [len(data.get('points', [])) for data in sectors_data.values()]
        sector_densities = [data.get('density', 0) for data in sectors_data.values()]

        # 1. 扇区点数分布
        ax1.bar(sector_ids, sector_counts, color=self.colors['sectors'], alpha=0.7)
        ax1.set_xlabel('扇区ID')
        ax1.set_ylabel('点数')
        ax1.set_title('各扇区点数分布')
        ax1.grid(True, alpha=0.3)

        # 2. 扇区密度分布
        ax2.bar(sector_ids, sector_densities, color=self.colors['density'], alpha=0.7)
        ax2.set_xlabel('扇区ID')
        ax2.set_ylabel('密度')
        ax2.set_title('各扇区密度分布')
        ax2.grid(True, alpha=0.3)

        # 3. 极坐标扇区图
        sector_angle = 360 / len(sector_ids) if sector_ids else 5
        angles = np.array([i * sector_angle for i in sector_ids])
        angles_rad = np.radians(angles)

        ax3 = plt.subplot(2, 2, 3, projection='polar')
        bars = ax3.bar(angles_rad, sector_counts,
                      width=np.radians(sector_angle),
                      color=self.colors['sectors'], alpha=0.7)
        ax3.set_title('扇区点数分布（极坐标）')
        ax3.set_theta_zero_location('E')
        ax3.set_theta_direction(1)

        # 4. 边界距离分布
        boundary_distances = []
        boundary_sectors = []

        for sector_id, data in sectors_data.items():
            if 'boundary_distance' in data and data['boundary_distance'] is not None:
                boundary_distances.append(data['boundary_distance'])
                boundary_sectors.append(sector_id)

        if boundary_distances:
            ax4.scatter(boundary_sectors, boundary_distances,
                       c=self.colors['boundary_points'], s=50, alpha=0.8)
            ax4.set_xlabel('扇区ID')
            ax4.set_ylabel('边界距离 (m)')
            ax4.set_title('各扇区边界距离')
            ax4.grid(True, alpha=0.3)
        else:
            ax4.text(0.5, 0.5, '无边界数据', ha='center', va='center', transform=ax4.transAxes)

        plt.suptitle(title, fontsize=16, fontweight='bold')
        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            print(f"✅ 扇区分析图已保存: {save_path}")

        return fig
    
    def plot_ellipse_fitting(self, x_coords: np.ndarray, y_coords: np.ndarray,
                           ellipse_params: Dict[str, float],
                           boundary_points: Optional[List[Tuple[float, float]]] = None,
                           title: str = "椭圆拟合结果",
                           save_path: Optional[str] = None) -> plt.Figure:
        """绘制椭圆拟合结果"""
        fig, ax = plt.subplots(figsize=self.figsize, dpi=self.dpi)

        # 绘制数据点
        ax.scatter(x_coords, y_coords,
                  c=self.colors['data_points'],
                  alpha=0.4, s=15,
                  label=f'数据点 ({len(x_coords)}个)')

        # 绘制边界点（如果有）
        if boundary_points:
            boundary_x = [p[0] for p in boundary_points]
            boundary_y = [p[1] for p in boundary_points]
            ax.scatter(boundary_x, boundary_y,
                      c=self.colors['boundary_points'],
                      s=30, alpha=0.8,
                      label=f'边界点 ({len(boundary_points)}个)')

        # 绘制椭圆
        a = ellipse_params.get('a', 100)
        b = ellipse_params.get('b', 50)
        center_x = ellipse_params.get('center_x', 0)
        center_y = ellipse_params.get('center_y', 0)
        rotation = ellipse_params.get('rotation', 0)

        ellipse_patch = patches.Ellipse(
            (center_x, center_y),
            2 * b,  # 宽度
            2 * a,  # 高度
            angle=np.degrees(rotation),
            linewidth=3,
            edgecolor=self.colors['ellipse'],
            facecolor='none',
            label=f'拟合椭圆 (a={a:.0f}m, b={b:.0f}m)'
        )
        ax.add_patch(ellipse_patch)

        # 添加原点标记
        ax.scatter(0, 0, c='red', s=100, marker='x', linewidth=3, label='本船位置')

        # 设置坐标轴
        ax.set_xlabel('横向距离 (m)', fontsize=12)
        ax.set_ylabel('纵向距离 (m)', fontsize=12)
        ax.set_title(title, fontsize=14, fontweight='bold')

        # 设置等比例和网格
        ax.set_aspect('equal', adjustable='box')
        ax.grid(True, alpha=0.3)
        ax.legend()

        # 设置坐标轴范围
        max_range = max(a, b) * 1.5
        ax.set_xlim(-max_range, max_range)
        ax.set_ylim(-max_range, max_range)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            print(f"✅ 椭圆拟合图已保存: {save_path}")

        return fig
    
    def plot_density_heatmap(self, x_coords: np.ndarray, y_coords: np.ndarray,
                           title: str = "密度热力图", 
                           save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制密度热力图
        
        Args:
            x_coords: X坐标数组
            y_coords: Y坐标数组
            title: 图标题
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        fig, ax = plt.subplots(figsize=self.figsize, dpi=self.dpi)
        
        # 创建密度热力图
        heatmap = ax.hexbin(x_coords, y_coords, gridsize=30, cmap='YlOrRd', alpha=0.8)
        
        # 添加颜色条
        cbar = plt.colorbar(heatmap, ax=ax)
        cbar.set_label('点密度', fontsize=12)
        
        # 添加原点标记
        ax.scatter(0, 0, c='blue', s=100, marker='x', linewidth=3, label='本船位置')
        
        # 设置坐标轴
        ax.set_xlabel('横向距离 (m)', fontsize=12)
        ax.set_ylabel('纵向距离 (m)', fontsize=12)
        ax.set_title(title, fontsize=14, fontweight='bold')
        
        # 设置等比例和网格
        ax.set_aspect('equal', adjustable='box')
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            print(f"✅ 密度热力图已保存: {save_path}")
        
        return fig
    
    def plot_fitting_comparison(self, x_coords: np.ndarray, y_coords: np.ndarray,
                              results: Dict[str, Dict],
                              title: str = "拟合方法对比",
                              save_path: Optional[str] = None) -> plt.Figure:
        """绘制不同拟合方法的对比图"""
        n_methods = len(results)
        cols = min(3, n_methods)
        rows = (n_methods + cols - 1) // cols

        fig, axes = plt.subplots(rows, cols, figsize=(5*cols, 4*rows), dpi=self.dpi)
        if n_methods == 1:
            axes = [axes]
        elif rows == 1:
            axes = axes.flatten()
        else:
            axes = axes.flatten()

        colors = ['#d62728', '#2ca02c', '#ff7f0e', '#1f77b4', '#9467bd']

        for i, (method, result) in enumerate(results.items()):
            ax = axes[i]

            # 绘制数据点
            ax.scatter(x_coords, y_coords,
                      c='lightblue', alpha=0.4, s=10,
                      label=f'数据点 ({len(x_coords)}个)')

            if result.get('success', True):
                # 绘制椭圆
                a = result.get('a', 100)
                b = result.get('b', 50)

                ellipse_patch = patches.Ellipse(
                    (0, 0), 2 * b, 2 * a,
                    linewidth=2,
                    edgecolor=colors[i % len(colors)],
                    facecolor='none',
                    label=f'a={a:.0f}m, b={b:.0f}m'
                )
                ax.add_patch(ellipse_patch)

                # 添加质量分数
                quality_score = result.get('quality_score', 0)
                quality_text = f'质量分数: {quality_score:.3f}'
                ax.text(0.02, 0.98, quality_text, transform=ax.transAxes,
                       verticalalignment='top',
                       bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
            else:
                error_msg = result.get('error_message', '未知错误')
                ax.text(0.5, 0.5, f'拟合失败\n{error_msg}',
                       ha='center', va='center', transform=ax.transAxes,
                       bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))

            # 添加原点标记
            ax.scatter(0, 0, c='red', s=50, marker='x', linewidth=2)

            ax.set_title(f'{method}方法', fontsize=12, fontweight='bold')
            ax.set_xlabel('横向距离 (m)')
            ax.set_ylabel('纵向距离 (m)')
            ax.set_aspect('equal', adjustable='box')
            ax.grid(True, alpha=0.3)
            ax.legend(fontsize=8)

        # 隐藏多余的子图
        for i in range(n_methods, len(axes)):
            axes[i].set_visible(False)

        plt.suptitle(title, fontsize=16, fontweight='bold')
        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            print(f"✅ 拟合对比图已保存: {save_path}")

        return fig
    
    def create_comprehensive_report(self, x_coords: np.ndarray, y_coords: np.ndarray,
                                  sectors_data: Dict = None,
                                  ellipse_params: Dict = None,
                                  boundary_points: List = None,
                                  output_dir: str = "vis") -> None:
        """创建综合分析报告"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        print(f"\n📊 生成综合分析报告...")

        # 1. 数据分布图
        self.plot_data_distribution(
            x_coords, y_coords,
            title="船舶相对位置数据分布",
            save_path=str(output_path / "01_data_distribution.png")
        )

        # 2. 密度热力图
        self.plot_density_heatmap(
            x_coords, y_coords,
            title="相对位置密度热力图",
            save_path=str(output_path / "02_density_heatmap.png")
        )

        # 3. 扇区分析图（如果有扇区数据）
        if sectors_data:
            self.plot_sector_distribution(
                sectors_data,
                title="扇区划分与密度分析",
                save_path=str(output_path / "03_sector_analysis.png")
            )

        # 4. 椭圆拟合结果图（如果有椭圆参数）
        if ellipse_params:
            self.plot_ellipse_fitting(
                x_coords, y_coords, ellipse_params,
                boundary_points=boundary_points,
                title="椭圆拟合结果",
                save_path=str(output_path / "04_ellipse_fitting.png")
            )

        print(f"✅ 综合分析报告已生成: {output_path}")


def main():
    """测试主函数"""
    # 生成测试数据
    np.random.seed(42)
    n_points = 1000
    
    # 生成椭圆分布的测试数据
    true_a, true_b = 200, 100
    angles = np.random.uniform(0, 2*np.pi, n_points)
    radii = np.random.gamma(2, 50, n_points)
    
    x_coords = radii * np.cos(angles) + np.random.normal(0, 20, n_points)
    y_coords = radii * np.sin(angles) + np.random.normal(0, 20, n_points)
    
    print(f"🧪 测试数据生成完成: {n_points} 个点")
    
    # 创建检测器和拟合器
    detector = DensityBoundaryDetector(sector_angle=10.0, debug=False)
    fitter = EllipseFitter(debug=False)
    visualizer = ShipDomainVisualizer()
    
    # 执行分析流程
    sectors = detector.divide_into_sectors(x_coords, y_coords)
    profiles = detector.calculate_sector_density_profiles()
    boundary_points = detector.detect_boundary_points(method='percentile')
    
    # 拟合椭圆
    results = fitter.compare_fitting_methods(x_coords, y_coords, boundary_points)
    
    # 生成综合报告
    visualizer.create_comprehensive_report(
        x_coords, y_coords, detector, fitter, results,
        output_dir="test_visualization_output"
    )


if __name__ == '__main__':
    main()
