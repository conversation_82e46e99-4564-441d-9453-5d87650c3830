"""
椭圆拟合模块
用于船舶领域拟合中的椭圆边界拟合

功能：
1. 传统椭圆拟合：基于最小二乘法的椭圆拟合
2. 边界点椭圆拟合：基于边界检测结果的椭圆拟合
3. 加权椭圆拟合：考虑点权重的椭圆拟合
4. 椭圆参数优化：椭圆参数的后处理和优化
5. 拟合质量评估：椭圆拟合质量的评估指标

作者：船舶领域拟合系统
日期：2025-08-01
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import leastsq, minimize
from scipy.spatial.distance import cdist
import math
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
import warnings

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


@dataclass
class EllipseParams:
    """椭圆参数结构"""
    a: float  # 长半轴（纵向，船头方向）
    b: float  # 短半轴（横向）
    center_x: float = 0.0  # 中心X坐标
    center_y: float = 0.0  # 中心Y坐标
    rotation: float = 0.0  # 旋转角度（弧度）
    
    def __post_init__(self):
        """确保参数为正数"""
        self.a = abs(self.a)
        self.b = abs(self.b)


@dataclass
class FittingResult:
    """拟合结果结构"""
    ellipse: EllipseParams
    data_count: int
    boundary_count: int
    fitting_method: str
    quality_score: float
    residual_error: float
    coverage_ratio: float
    success: bool = True
    error_message: str = ""


class EllipseFitter:
    """椭圆拟合器"""
    
    def __init__(self, debug: bool = False):
        """
        初始化椭圆拟合器
        
        Args:
            debug: 是否开启调试模式
        """
        self.debug = debug
        self.fitting_history: List[FittingResult] = []
        
        print(f"🔧 椭圆拟合器初始化完成")
    
    def fit_ellipse_traditional(self, x_coords: np.ndarray, y_coords: np.ndarray, 
                              weights: Optional[np.ndarray] = None) -> FittingResult:
        """
        传统椭圆拟合方法（最小二乘法）
        
        Args:
            x_coords: X坐标数组
            y_coords: Y坐标数组
            weights: 点权重数组（可选）
            
        Returns:
            拟合结果
        """
        try:
            x_coords = np.array(x_coords)
            y_coords = np.array(y_coords)
            
            if len(x_coords) < 5:
                return FittingResult(
                    ellipse=EllipseParams(0, 0),
                    data_count=len(x_coords),
                    boundary_count=0,
                    fitting_method="traditional",
                    quality_score=0.0,
                    residual_error=float('inf'),
                    coverage_ratio=0.0,
                    success=False,
                    error_message="数据点不足"
                )
            
            # 初始参数估计
            a_initial = max((np.max(y_coords) - np.min(y_coords)) / 2, 10.0)
            b_initial = max((np.max(x_coords) - np.min(x_coords)) / 2, 10.0)
            
            # 最小二乘拟合
            if weights is not None:
                # 加权拟合
                params, _ = leastsq(self._weighted_ellipse_equation, [a_initial, b_initial], 
                                 args=(x_coords, y_coords, weights))
            else:
                # 等权重拟合
                params, _ = leastsq(self._ellipse_equation, [a_initial, b_initial], 
                                 args=(x_coords, y_coords))
            
            a, b = abs(params[0]), abs(params[1])
            
            if a <= 0 or b <= 0:
                raise ValueError("拟合得到无效的椭圆参数")
            
            # 创建椭圆参数
            ellipse = EllipseParams(a=a, b=b)
            
            # 计算拟合质量
            quality_metrics = self._evaluate_fitting_quality(x_coords, y_coords, ellipse)
            
            result = FittingResult(
                ellipse=ellipse,
                data_count=len(x_coords),
                boundary_count=len(x_coords),
                fitting_method="traditional",
                quality_score=quality_metrics['quality_score'],
                residual_error=quality_metrics['residual_error'],
                coverage_ratio=quality_metrics['coverage_ratio']
            )
            
            self.fitting_history.append(result)
            return result
            
        except Exception as e:
            if self.debug:
                print(f"传统椭圆拟合失败: {e}")
            
            return FittingResult(
                ellipse=EllipseParams(0, 0),
                data_count=len(x_coords) if 'x_coords' in locals() else 0,
                boundary_count=0,
                fitting_method="traditional",
                quality_score=0.0,
                residual_error=float('inf'),
                coverage_ratio=0.0,
                success=False,
                error_message=str(e)
            )
    
    def fit_ellipse_from_boundary_points(self, boundary_points: List[Tuple[float, float]]) -> FittingResult:
        """
        基于边界点拟合椭圆
        
        Args:
            boundary_points: 边界点坐标列表
            
        Returns:
            拟合结果
        """
        try:
            if len(boundary_points) < 5:
                return FittingResult(
                    ellipse=EllipseParams(0, 0),
                    data_count=0,
                    boundary_count=len(boundary_points),
                    fitting_method="boundary_points",
                    quality_score=0.0,
                    residual_error=float('inf'),
                    coverage_ratio=0.0,
                    success=False,
                    error_message="边界点不足"
                )
            
            # 分离坐标
            x_coords = np.array([p[0] for p in boundary_points])
            y_coords = np.array([p[1] for p in boundary_points])
            
            # 使用传统方法拟合边界点
            result = self.fit_ellipse_traditional(x_coords, y_coords)
            result.fitting_method = "boundary_points"
            result.boundary_count = len(boundary_points)
            
            return result
            
        except Exception as e:
            if self.debug:
                print(f"边界点椭圆拟合失败: {e}")
            
            return FittingResult(
                ellipse=EllipseParams(0, 0),
                data_count=0,
                boundary_count=len(boundary_points) if 'boundary_points' in locals() else 0,
                fitting_method="boundary_points",
                quality_score=0.0,
                residual_error=float('inf'),
                coverage_ratio=0.0,
                success=False,
                error_message=str(e)
            )
    
    def fit_ellipse_robust(self, x_coords: np.ndarray, y_coords: np.ndarray, 
                          outlier_threshold: float = 2.0) -> FittingResult:
        """
        鲁棒椭圆拟合（去除异常值）
        
        Args:
            x_coords: X坐标数组
            y_coords: Y坐标数组
            outlier_threshold: 异常值阈值（标准差倍数）
            
        Returns:
            拟合结果
        """
        try:
            x_coords = np.array(x_coords)
            y_coords = np.array(y_coords)
            
            if len(x_coords) < 10:
                return self.fit_ellipse_traditional(x_coords, y_coords)
            
            # 初步拟合
            initial_result = self.fit_ellipse_traditional(x_coords, y_coords)
            if not initial_result.success:
                return initial_result
            
            # 计算每个点到椭圆的距离
            distances = self._calculate_point_to_ellipse_distances(
                x_coords, y_coords, initial_result.ellipse
            )
            
            # 识别异常值
            mean_dist = np.mean(distances)
            std_dist = np.std(distances)
            threshold = mean_dist + outlier_threshold * std_dist
            
            # 过滤异常值
            inlier_mask = distances <= threshold
            clean_x = x_coords[inlier_mask]
            clean_y = y_coords[inlier_mask]
            
            if len(clean_x) < 5:
                return initial_result  # 如果过滤后点太少，返回初始结果
            
            # 重新拟合
            result = self.fit_ellipse_traditional(clean_x, clean_y)
            result.fitting_method = "robust"
            result.data_count = len(x_coords)  # 原始数据点数
            
            if self.debug:
                print(f"鲁棒拟合: 原始{len(x_coords)}点 -> 清理后{len(clean_x)}点")
            
            return result
            
        except Exception as e:
            if self.debug:
                print(f"鲁棒椭圆拟合失败: {e}")
            
            return self.fit_ellipse_traditional(x_coords, y_coords)
    
    def optimize_ellipse_parameters(self, ellipse: EllipseParams, 
                                  x_coords: np.ndarray, y_coords: np.ndarray) -> EllipseParams:
        """
        优化椭圆参数
        
        Args:
            ellipse: 初始椭圆参数
            x_coords: X坐标数组
            y_coords: Y坐标数组
            
        Returns:
            优化后的椭圆参数
        """
        try:
            # 定义优化目标函数
            def objective(params):
                a, b = abs(params[0]), abs(params[1])
                if a <= 0 or b <= 0:
                    return float('inf')
                
                test_ellipse = EllipseParams(a=a, b=b)
                distances = self._calculate_point_to_ellipse_distances(x_coords, y_coords, test_ellipse)
                return np.mean(np.abs(distances))  # 最小化平均绝对距离
            
            # 优化
            initial_params = [ellipse.a, ellipse.b]
            result = minimize(objective, initial_params, method='Nelder-Mead')
            
            if result.success:
                optimized_a, optimized_b = abs(result.x[0]), abs(result.x[1])
                return EllipseParams(a=optimized_a, b=optimized_b)
            else:
                return ellipse
                
        except Exception as e:
            if self.debug:
                print(f"椭圆参数优化失败: {e}")
            return ellipse
    
    def _ellipse_equation(self, params: List[float], x: np.ndarray, y: np.ndarray) -> np.ndarray:
        """椭圆方程（用于最小二乘拟合）"""
        a, b = params
        if a <= 0 or b <= 0:
            return np.full_like(x, float('inf'))
        return (x / b) ** 2 + (y / a) ** 2 - 1
    
    def _weighted_ellipse_equation(self, params: List[float], x: np.ndarray, 
                                 y: np.ndarray, weights: np.ndarray) -> np.ndarray:
        """加权椭圆方程"""
        residuals = self._ellipse_equation(params, x, y)
        return residuals * np.sqrt(weights)
    
    def _calculate_point_to_ellipse_distances(self, x_coords: np.ndarray, y_coords: np.ndarray, 
                                            ellipse: EllipseParams) -> np.ndarray:
        """计算点到椭圆的距离"""
        distances = []
        
        for x, y in zip(x_coords, y_coords):
            # 计算点到椭圆中心的距离
            distance_to_center = np.sqrt(x**2 + y**2)
            
            if distance_to_center == 0:
                distances.append(0)
                continue
            
            # 计算角度
            angle = np.arctan2(y, x)
            
            # 椭圆在该方向上的半径
            r_ellipse = (ellipse.a * ellipse.b) / np.sqrt(
                (ellipse.b * np.cos(angle))**2 + (ellipse.a * np.sin(angle))**2
            )
            
            # 距离差
            distance_diff = distance_to_center - r_ellipse
            distances.append(distance_diff)
        
        return np.array(distances)
    
    def _evaluate_fitting_quality(self, x_coords: np.ndarray, y_coords: np.ndarray, 
                                ellipse: EllipseParams) -> Dict[str, float]:
        """评估拟合质量"""
        try:
            # 计算残差
            distances = self._calculate_point_to_ellipse_distances(x_coords, y_coords, ellipse)
            residual_error = np.sqrt(np.mean(distances**2))  # RMSE
            
            # 计算覆盖率（椭圆内的点的比例）
            inside_count = sum(1 for d in distances if d <= 0)
            coverage_ratio = inside_count / len(distances) if len(distances) > 0 else 0
            
            # 综合质量分数（越低越好）
            quality_score = residual_error * (1 - coverage_ratio + 0.1)
            
            return {
                'residual_error': residual_error,
                'coverage_ratio': coverage_ratio,
                'quality_score': quality_score
            }
            
        except Exception as e:
            if self.debug:
                print(f"质量评估失败: {e}")
            return {
                'residual_error': float('inf'),
                'coverage_ratio': 0.0,
                'quality_score': float('inf')
            }
    
    def get_ellipse_points(self, ellipse: EllipseParams, num_points: int = 100) -> Tuple[np.ndarray, np.ndarray]:
        """获取椭圆边界点坐标"""
        theta = np.linspace(0, 2 * np.pi, num_points)
        x = ellipse.b * np.cos(theta) + ellipse.center_x
        y = ellipse.a * np.sin(theta) + ellipse.center_y
        
        # 如果有旋转，应用旋转变换
        if ellipse.rotation != 0:
            cos_rot = np.cos(ellipse.rotation)
            sin_rot = np.sin(ellipse.rotation)
            x_rot = cos_rot * (x - ellipse.center_x) - sin_rot * (y - ellipse.center_y) + ellipse.center_x
            y_rot = sin_rot * (x - ellipse.center_x) + cos_rot * (y - ellipse.center_y) + ellipse.center_y
            return x_rot, y_rot
        
        return x, y
    
    def compare_fitting_methods(self, x_coords: np.ndarray, y_coords: np.ndarray, 
                              boundary_points: Optional[List[Tuple[float, float]]] = None) -> Dict[str, FittingResult]:
        """比较不同拟合方法的结果"""
        results = {}
        
        # 传统方法
        results['traditional'] = self.fit_ellipse_traditional(x_coords, y_coords)
        
        # 鲁棒方法
        results['robust'] = self.fit_ellipse_robust(x_coords, y_coords)
        
        # 边界点方法（如果提供）
        if boundary_points:
            results['boundary_points'] = self.fit_ellipse_from_boundary_points(boundary_points)
        
        return results
    
    def get_best_fitting_result(self, results: Dict[str, FittingResult]) -> FittingResult:
        """从多个拟合结果中选择最佳结果"""
        valid_results = {k: v for k, v in results.items() if v.success}
        
        if not valid_results:
            # 如果没有成功的结果，返回第一个
            return list(results.values())[0]
        
        # 选择质量分数最低的结果
        best_method = min(valid_results.keys(), key=lambda k: valid_results[k].quality_score)
        return valid_results[best_method]


def main():
    """测试主函数"""
    # 生成测试数据
    np.random.seed(42)
    n_points = 500
    
    # 生成椭圆分布的测试数据
    true_a, true_b = 200, 100
    angles = np.random.uniform(0, 2*np.pi, n_points)
    
    # 在椭圆边界附近生成点
    radii = np.random.uniform(0.8, 1.2, n_points)  # 椭圆边界附近
    x_coords = true_b * radii * np.cos(angles) + np.random.normal(0, 10, n_points)
    y_coords = true_a * radii * np.sin(angles) + np.random.normal(0, 10, n_points)
    
    print(f"🧪 测试数据生成完成: {n_points} 个点")
    print(f"   真实椭圆参数: a={true_a}, b={true_b}")
    
    # 创建拟合器
    fitter = EllipseFitter(debug=True)
    
    # 比较不同拟合方法
    results = fitter.compare_fitting_methods(x_coords, y_coords)
    
    # 打印结果
    print(f"\n📊 拟合结果比较:")
    for method, result in results.items():
        if result.success:
            print(f"   {method}: a={result.ellipse.a:.1f}, b={result.ellipse.b:.1f}, "
                  f"质量分数={result.quality_score:.3f}")
        else:
            print(f"   {method}: 拟合失败 - {result.error_message}")
    
    # 选择最佳结果
    best_result = fitter.get_best_fitting_result(results)
    print(f"\n🏆 最佳拟合方法: {best_result.fitting_method}")
    print(f"   参数: a={best_result.ellipse.a:.1f}, b={best_result.ellipse.b:.1f}")
    print(f"   误差: {abs(best_result.ellipse.a - true_a):.1f}, {abs(best_result.ellipse.b - true_b):.1f}")


if __name__ == '__main__':
    main()
